import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, Droplet, Phone, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import gsap from 'gsap';

// Glass card component
const GlassCard: React.FC<{ className?: string; children: React.ReactNode }> = ({ children, className = '' }) => {
  const gradientRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (gradientRef.current) {
      // GSAP animation for a subtle, shifting gradient
      gsap.to(gradientRef.current, {
        '--gradient-color-1': 'rgba(59, 130, 246, 0.2)', // blue-500 with opacity
        '--gradient-color-2': 'rgba(96, 165, 250, 0.3)', // blue-400 with opacity
        '--gradient-color-3': 'rgba(34, 211, 238, 0.2)', // cyan-400 with opacity
        duration: 7,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
      });
      gsap.to(gradientRef.current, {
        backgroundPosition: '200% center',
        duration: 15,
        repeat: -1,
        ease: 'linear',
      })
    }
  }, []);

  return (
    <div
      className={`relative backdrop-blur-xl bg-white/5 dark:bg-gray-900/5 border border-white/10 dark:border-gray-700/30 rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 hover:shadow-3xl ${className}`}
      id="home"
    >
      <div
        ref={gradientRef}
        className="absolute inset-0 -z-10 opacity-70"
        style={{
          backgroundImage: 'linear-gradient(60deg, var(--gradient-color-1, rgba(59, 130, 246, 0.1)), var(--gradient-color-2, rgba(96, 165, 250, 0.15)) 50%, var(--gradient-color-3, rgba(34, 211, 238, 0.1)) 100%)',
          backgroundSize: '300% 300%', // Made larger for smoother animation
        }}
      />
      {children}
    </div>
  );
};

const Hero: React.FC = () => {
  const heroRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Floating animation for elements (kept from original)
    const floatingElements = document.querySelectorAll('.floating-element');
    floatingElements.forEach((el) => {
      gsap.to(el, {
        y: 8, // Slightly reduced y for subtlety
        duration: 3.5,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
      });
    });
  }, []);
  
  // Updated to use landingpage.jpg from the public folder
  const heroBackgroundImage = 'url("/landingpage.jpg")'; 

  return (
    <section
      ref={heroRef}
      className="min-h-screen relative overflow-hidden flex items-center justify-center md:justify-start bg-cover bg-center p-3 sm:p-4 md:p-8 w-full"
      style={{
        backgroundImage: heroBackgroundImage,
        backgroundAttachment: window.innerWidth > 768 ? 'fixed' : 'scroll', // Disable parallax on mobile for performance
      }}
    >
      {/* Enhanced overlay for better mobile readability */}
      <div className="absolute inset-0 bg-black/50 sm:bg-black/40 dark:bg-black/70 dark:sm:bg-black/60 -z-0"></div>

      {/* Mobile-optimized content container */}
      <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8 relative z-10 w-full md:w-auto md:ml-[5%] lg:ml-[10%]">
        <GlassCard className="p-6 sm:p-8 md:p-10 lg:p-12 w-full md:max-w-xl lg:max-w-2xl">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="text-left" 
          >
           
            
            {/* Mobile-optimized heading */}
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold text-white dark:text-gray-50 mb-4 sm:mb-5 leading-tight tracking-tight antialiased">
              An Original Equipment Manufacturer (OEM) for Water and Wastewater Engineering
            </h1>

            {/* Mobile-optimized description */}
            <p className="text-base sm:text-lg md:text-xl text-gray-200 dark:text-gray-300 mb-8 sm:mb-10 max-w-3xl leading-relaxed antialiased">
              PAZOGEN is your trusted partner in water and wastewater solutions.
            </p>

            {/* Enhanced Mobile-First CTA Button */}
            <motion.div
              className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-start"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Link
                  to="/get-a-quote"
                  className="group w-full sm:w-auto px-8 py-4 sm:py-3.5 bg-gradient-to-r from-blue-500 to-cyan-400 text-white font-bold sm:font-semibold rounded-2xl sm:rounded-lg hover:from-blue-600 hover:to-cyan-500 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-center inline-flex items-center justify-center text-base sm:text-sm active:scale-95"
                >
                  GET A FREE QUOTE
                  <ArrowRight className="w-5 h-5 ml-2 transition-transform duration-300 ease-in-out group-hover:translate-x-1" />
                </Link>
              </motion.div>
            </motion.div>
          </motion.div>
        </GlassCard>
      </div>
      
      {/* Enhanced Mobile WhatsApp Floating Action Button */}
      <motion.div
        className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 md:bottom-8 md:right-8 z-50 group"
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ delay: 1, duration: 0.5, type: "spring", stiffness: 200 }}
      >
        <motion.a
          href="https://wa.me/+27659643597"
          target="_blank"
          rel="noopener noreferrer"
          className="relative overflow-hidden backdrop-blur-xl bg-green-500/90 hover:bg-green-600/90 border border-green-400/30 text-white p-4 sm:p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 ease-in-out flex items-center justify-center active:scale-90"
          aria-label="Chat on WhatsApp"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <div className="absolute inset-0 -z-10 opacity-70 animate-pulse"
               style={{
                 background: 'linear-gradient(60deg, rgba(34, 197, 94, 0.3), rgba(22, 163, 74, 0.4) 50%, rgba(21, 128, 61, 0.3) 100%)',
                 backgroundSize: '300% 300%',
               }}
          />
          <svg
            className="relative z-10 w-6 h-6 sm:w-7 sm:h-7"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.685"/>
          </svg>
        </motion.a>
      </motion.div>
      
      <style jsx global>{`
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>

      {/* Enhanced Mobile Scroll Indicator */}
      <motion.div
        className="absolute bottom-20 sm:bottom-24 md:bottom-12 left-0 right-0 mx-auto w-fit flex flex-col items-center z-10"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2, duration: 0.8, ease: 'easeOut' }}
      >
        <motion.span
          className="text-xs sm:text-sm text-gray-200 dark:text-gray-300 mb-2 text-center font-medium"
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          Scroll to explore
        </motion.span>
        <motion.div
          animate={{ y: [0, 8, 0] }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
        >
          <ChevronDown className="w-5 h-5 sm:w-6 sm:h-6 text-gray-200 dark:text-gray-300" />
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;